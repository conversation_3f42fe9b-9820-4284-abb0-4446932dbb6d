import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsNumber, Min } from 'class-validator';
import { Type } from 'class-transformer';

export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
}

export class GetBookingsDto {
  @ApiProperty({
    description: 'ID của booking page',
    required: false,
  })
  @IsOptional()
  @IsString()
  bookingPageId?: string;

  @ApiProperty({
    description: 'Email của khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString()
  customerEmail?: string;

  @ApiProperty({
    description: 'Trạng thái của booking',
    enum: BookingStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(BookingStatus)
  status?: BookingStatus;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> bắt đầu (YYYY-MM-DD)',
    required: false,
  })
  @IsOptional()
  @IsString()
  dateFrom?: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> kết thúc (YYYY-MM-DD)',
    required: false,
  })
  @IsOptional()
  @IsString()
  dateTo?: string;

  @ApiProperty({
    description: 'Số lượng kết quả trả về',
    required: false,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({
    description: 'ID của booking cuối cùng trong trang trước (cursor)',
    required: false,
  })
  @IsOptional()
  @IsString()
  after?: string;

  @ApiProperty({
    description: 'ID của booking đầu tiên trong trang trước (cursor)',
    required: false,
  })
  @IsOptional()
  @IsString()
  before?: string;
} 