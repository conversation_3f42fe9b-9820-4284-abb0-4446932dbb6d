export const REDIS_KEYS = {
  ACCESS_TOKEN: (token) => `auth:access:${token}`,
  REFRESH_TOKEN: (token) => `auth:refresh:${token}`,
  BLACKLIST: (token) => `auth:blacklist:${token}`,
  USER_SESSIONS: (userId) => `auth:user:${userId}:sessions`,
  RATE_LIMIT: (ip) => `auth:rate:${ip}`,
};

// 2. Token Configuration
export const TOKEN_CONFIG = {
  ACCESS_TOKEN_TTL: 7 * 24 * 60 * 60, // 7 days
  REFRESH_TOKEN_TTL: 30 * 24 * 60 * 60, // 30 days
  BLACKLIST_TTL: 30 * 24 * 60 * 60, // 30 days (same as refresh token)
  MAX_SESSIONS_PER_USER: 5,
};

export const getRefreshToken = (token: string) => {
  return REDIS_KEYS.REFRESH_TOKEN(token);
};

export const getAccessToken = (token: string) => {
  return REDIS_KEYS.ACCESS_TOKEN(token);
};
