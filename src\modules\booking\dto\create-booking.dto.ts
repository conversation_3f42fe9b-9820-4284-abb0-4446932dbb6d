import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEmail,
  IsEnum,
  IsMongoId,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPhoneNumber,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

export class BookingSlotDto {
  @ApiProperty({
    description: 'Date of the booking',
    example: '2025-05-22T14:48:29.780Z',
  })
  @IsString()
  @IsNotEmpty()
  date: string;

  @ApiProperty({
    description: 'Field ID',
    example: 'field-1',
  })
  @IsString()
  @IsNotEmpty()
  field: string;

  @ApiProperty({
    description: 'Time slot',
    example: '08:00',
  })
  @IsString()
  @IsNotEmpty()
  time: string;
}

export class CreateBookingDto {
  bookingCode?: string;

  @ApiProperty({
    description: 'Booking page ID',
    example: '68261cde094b5f3e38d3ae25',
  })
  @IsMongoId()
  @IsNotEmpty()
  bookingPageId: string;

  @ApiProperty({
    description: 'Customer name',
    example: '<PERSON><PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  customerName: string;

  @ApiProperty({
    description: 'Customer email',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  customerEmail: string;

  @ApiProperty({
    description: 'Customer phone number',
    example: '0364982187',
  })
  @IsPhoneNumber('VN')
  @IsNotEmpty()
  customerPhone: string;

  @ApiProperty({
    description: 'Booking date',
    example: '2025-05-22',
  })
  @IsString()
  @IsNotEmpty()
  bookingDate: string;

  @ApiProperty({
    description: 'Booking slots',
    type: [BookingSlotDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BookingSlotDto)
  @IsNotEmpty()
  bookingSlots: BookingSlotDto[];

  @ApiProperty({
    description: 'Payment method',
    example: 'COD',
    enum: ['COD', 'BANK_TRANSFER', 'CREDIT_CARD', 'MOMO'],
  })
  @IsEnum(['COD', 'BANK_TRANSFER', 'CREDIT_CARD', 'MOMO'])
  @IsNotEmpty()
  paymentMethod: string;

  @ApiProperty({
    description: 'Quantity',
    example: 1,
    default: 1,
  })
  @IsNumber()
  @Min(1)
  @IsOptional()
  quantity?: number;

  @ApiProperty({
    description: 'Booking page slug',
    example: 'the-b-hoang-van-thu',
  })
  @IsString()
  @IsNotEmpty()
  slug: string;
}
