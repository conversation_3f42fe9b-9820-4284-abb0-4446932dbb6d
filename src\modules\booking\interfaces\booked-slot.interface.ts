export interface BookedSlot {
  field: string;
  time: string;
  date: string;
  bookingId: string;
  bookingCode: string;
  status: string; // 'pending', 'confirmed', 'cancelled', 'completed'
}

export interface BookedSlotsResponse {
  bookingPageId: string;
  date: string;
  bookedSlots: BookedSlot[];
}

// Enhanced booking slot with full booking details
export interface BookingSlotWithDetails {
  field: string;
  fieldName: string;
  time: string;
  date: string;
  bookingId: string;
  bookingCode: string;
  status: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  quantity: number;
  paymentMethod: string;
  createdAt: Date;
}

export interface BookingSlotsFilterResponse {
  slots: BookingSlotWithDetails[];
  total: number;
  limit: number;
  offset: number;
}

// Filter interfaces for better type safety
export interface BookingFilterOptions {
  bookingPageId: string;
  date?: string;
  dateFrom?: string;
  dateTo?: string;
  field?: string;
  time?: string;
  status?: string;
  limit?: string | number;
  offset?: string | number;
}

export interface BookingFilterResult {
  bookings: any[]; // Using any[] to avoid circular dependency with Booking
  total: number;
  limit: number;
  offset: number;
}

// Constants for default values
export const BOOKING_FILTER_DEFAULTS = {
  LIMIT: 20,
  OFFSET: 0,
  MAX_LIMIT: 200,
} as const;
