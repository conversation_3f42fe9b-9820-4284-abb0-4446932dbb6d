import { Module } from '@nestjs/common';
import { BookingPublicController } from './controllers/booking-public.controller';
import { BookingPagePublicController } from './controllers/booking-page-public.controller';
import { BookingModule } from '../booking/booking.module';
import { BookingPageModule } from '../booking-page/booking-page.module';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [BookingModule, BookingPageModule, CommonModule],
  controllers: [BookingPublicController, BookingPagePublicController],
})
export class PublicModule {}
