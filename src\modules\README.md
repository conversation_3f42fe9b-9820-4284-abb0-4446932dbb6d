# Modules Structure

## Overview
Cấu trúc modules đã được tổ chức lại để tách biệt rõ ràng giữa các route dành cho agent và public.

## New Structure

### Agent Module (`/src/modules/agent/`)
Chứa tất cả các routes dành cho **agents** (yêu cầu authentication):
- **Route prefix**: `/agent/`
- **Controllers**:
  - `BookingAgentController` - `/agent/booking`
  - `BookingPageAgentController` - `/agent/booking-page`
- **Features**:
  - Tất cả routes đều yêu cầu authentication (AuthGuard)
  - Agents có thể quản lý booking pages của họ
  - Agents có thể xem và cập nhật bookings

### Public Module (`/src/modules/public/`)
Chứa tất cả các routes dành cho **end users** (không yêu cầu authentication):
- **Route prefix**: `/public/`
- **Controllers**:
  - `BookingPublicController` - `/public/booking`
  - `BookingPagePublicController` - `/public/booking-page`
- **Features**:
  - Không yêu cầu authentication
  - End users chỉ có thể xem public booking pages
  - End users có thể tạo bookings

## Route Examples

### Agent Routes (Authenticated)
```
GET    /agent/booking-page          # Get all booking pages owned by agent
POST   /agent/booking-page          # Create new booking page
GET    /agent/booking-page/:id      # Get booking page by ID
PUT    /agent/booking-page/:id      # Update booking page
DELETE /agent/booking-page/:id      # Delete booking page

GET    /agent/booking               # Get bookings for a booking page
GET    /agent/booking/:id           # Get booking by ID
PUT    /agent/booking/:id/status    # Update booking status
```

### Public Routes (No Authentication)
```
GET    /public/booking-page         # Get all public booking pages
GET    /public/booking-page/:id     # Get public booking page by ID
GET    /public/booking-page/slug/:slug # Get public booking page by slug

POST   /public/booking              # Create new booking
GET    /public/booking              # Get bookings by email
GET    /public/booking/:id          # Get booking by ID
GET    /public/booking/slots/booked # Get booked slots
```

## Benefits

1. **Dễ tìm kiếm**: Tất cả agent routes ở `/agent/`, public routes ở `/public/`
2. **Bảo mật rõ ràng**: Agent module có AuthGuard, public module không
3. **Dễ bảo trì**: Logic riêng biệt cho từng loại user
4. **Scalable**: Dễ dàng thêm controllers mới vào đúng module

## Core Modules

### Booking Module (`/src/modules/booking/`)
- **Purpose**: Chứa business logic và data access cho bookings
- **Exports**: `BookingService`, `BookingRepository`
- **No Controllers**: Controllers đã được di chuyển vào Agent/Public modules

### BookingPage Module (`/src/modules/booking-page/`)
- **Purpose**: Chứa business logic và data access cho booking pages
- **Exports**: `BookingPageService`, `BookingPageRepository`
- **No Controllers**: Controllers đã được di chuyển vào Agent/Public modules
