import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  Query,
  ValidationPipe,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { BookingService } from '../../booking/booking.service';
import { CreateBookingDto } from '../../booking/dto/create-booking.dto';
import { ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
import { BookedSlotsResponse } from '../../booking/interfaces/booked-slot.interface';

@ApiTags('public/booking')
@Controller('public/booking')
export class BookingPublicController {
  constructor(private readonly bookingService: BookingService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new booking' })
  @ApiResponse({
    status: 201,
    description: 'Booking created successfully',
  })
  async create(@Body(new ValidationPipe()) createBookingDto: CreateBookingDto) {
    console.log(
      'Controller: Creating new booking',
      JSON.stringify(createBookingDto, null, 2),
    );

    try {
      const data = await this.bookingService.create(createBookingDto);
      console.log(
        `Controller: Booking created successfully with ID: ${data._id}`,
      );
      return { data };
    } catch (error: any) {
      console.error('Controller: Error creating booking:', error);

      // Extract the error message from the error object
      let errorMessage = 'Failed to create booking';

      if (error.response?.message) {
        errorMessage = error.response.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.error(`Controller: Throwing error with message: ${errorMessage}`);
      throw new NotFoundException(errorMessage);
    }
  }

  @Get()
  @ApiOperation({ summary: 'Get bookings by email' })
  @ApiResponse({ status: 200, description: 'Return bookings' })
  @ApiQuery({
    name: 'email',
    required: true,
    description: 'Customer email',
  })
  async findByEmail(@Query('email') email: string) {
    if (!email) {
      throw new NotFoundException('Email is required');
    }

    const data = await this.bookingService.findAll({
      customerEmail: email,
    });
    return { data };
  }

  @Get('slots/booked')
  @ApiOperation({
    summary: 'Get all booked slots for a specific booking page and date',
  })
  @ApiQuery({
    name: 'bookingPageId',
    required: true,
    description: 'Booking page ID',
  })
  @ApiQuery({
    name: 'date',
    required: true,
    description: 'Date to check (YYYY-MM-DD)',
  })
  async getBookedSlots(
    @Query('bookingPageId') bookingPageId: string,
    @Query('date') date: string,
  ): Promise<{ data: BookedSlotsResponse }> {
    console.log(
      `Controller: Getting booked slots for bookingPageId=${bookingPageId}, date=${date}`,
    );

    if (!bookingPageId) {
      throw new BadRequestException('Booking page ID is required');
    }

    if (!date) {
      throw new BadRequestException('Date is required');
    }

    try {
      // Validate bookingPageId format (MongoDB ObjectId)
      if (!/^[0-9a-fA-F]{24}$/.test(bookingPageId)) {
        throw new BadRequestException('Invalid booking page ID format');
      }

      // Validate date format (YYYY-MM-DD)
      if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
        throw new BadRequestException('Invalid date format. Use YYYY-MM-DD');
      }

      const data = await this.bookingService.getBookedSlots(
        bookingPageId,
        date,
      );
      return { data };
    } catch (error: any) {
      console.error('Controller: Error getting booked slots:', error);

      if (error instanceof BadRequestException) {
        throw error;
      }

      // Extract the error message from the error object
      let errorMessage = 'Failed to get booked slots';

      if (error.response?.message) {
        errorMessage = error.response.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      throw new NotFoundException(errorMessage);
    }
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a booking by ID' })
  async findOne(@Param('id') id: string) {
    try {
      const data = await this.bookingService.findOne(id);
      return { data };
    } catch (error: any) {
      throw new NotFoundException(error?.message || 'Booking not found');
    }
  }
}
