import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { BookingPageService } from './booking-page.service';
import { BookingPageRepository } from './repositories/booking-page.repository';
import { bookingPageModelDefinition } from './schemas/booking-page.schema';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [
    MongooseModule.forFeature([bookingPageModelDefinition]),
    CommonModule,
  ],
  controllers: [],
  providers: [BookingPageService, BookingPageRepository],
  exports: [BookingPageService, BookingPageRepository],
})
export class BookingPageModule {}
