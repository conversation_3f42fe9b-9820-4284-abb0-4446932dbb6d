import { Module } from '@nestjs/common';
import { BookingAgentController } from './controllers/booking-agent.controller';
import { BookingPageAgentController } from './controllers/booking-page-agent.controller';
import { BookingModule } from '../booking/booking.module';
import { BookingPageModule } from '../booking-page/booking-page.module';
import { CommonModule } from 'src/common/common.module';

@Module({
  imports: [BookingModule, BookingPageModule, CommonModule],
  controllers: [BookingAgentController, BookingPageAgentController],
})
export class AgentModule {}
